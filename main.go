package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
)

// 定义一个用户结构体，用于演示 JSON 序列化和反序列化
type User struct {
	ID       int       `json:"id"`
	Name     string    `json:"name"`
	Email    string    `json:"email"`
	Age      int       `json:"age"`
	IsActive bool      `json:"is_active"`
	Created  time.Time `json:"created"`
	Tags     []string  `json:"tags,omitempty"` // omitempty 表示如果为空则不包含在 JSON 中
}

// 定义一个产品结构体
type Product struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	Price       float64 `json:"price"`
	Description string  `json:"description,omitempty"`
	InStock     bool    `json:"in_stock"`
}

// 定义一个订单结构体，包含嵌套结构
type Order struct {
	ID       int       `json:"id"`
	User     User      `json:"user"`
	Products []Product `json:"products"`
	Total    float64   `json:"total"`
	Status   string    `json:"status"`
	Created  time.Time `json:"created"`
}

func main() {
	fmt.Println("=== JSON Marshal 和 Unmarshal 演示 ===\n")

	// 演示1: 基本的 Marshal 和 Unmarshal
	fmt.Println("1. 基本的 Marshal 和 Unmarshal 演示:")
	demonstrateBasicJSON()

	fmt.Println("\n==================================================\n")

	// 演示2: 复杂结构的 Marshal 和 Unmarshal
	fmt.Println("2. 复杂结构的 Marshal 和 Unmarshal 演示:")
	demonstrateComplexJSON()

	fmt.Println("\n==================================================\n")

	// 演示3: JSON 标签的使用
	fmt.Println("3. JSON 标签的使用演示:")
	demonstrateJSONTags()

	fmt.Println("\n==================================================\n")

	// 演示4: 错误处理
	fmt.Println("4. 错误处理演示:")
	demonstrateErrorHandling()
}

// 演示基本的 JSON 操作
func demonstrateBasicJSON() {
	// 创建一个用户对象
	user := User{
		ID:       1,
		Name:     "张三",
		Email:    "<EMAIL>",
		Age:      25,
		IsActive: true,
		Created:  time.Now(),
		Tags:     []string{"开发者", "Go语言"},
	}

	fmt.Printf("原始用户对象: %+v\n", user)

	// Marshal: 将 Go 对象转换为 JSON 字符串
	jsonData, err := json.Marshal(user)
	if err != nil {
		log.Fatalf("Marshal 失败: %v", err)
	}

	fmt.Printf("Marshal 后的 JSON: %s\n", string(jsonData))

	// 美化输出的 JSON
	prettyJSON, err := json.MarshalIndent(user, "", "  ")
	if err != nil {
		log.Fatalf("MarshalIndent 失败: %v", err)
	}

	fmt.Printf("美化后的 JSON:\n%s\n", string(prettyJSON))

	// Unmarshal: 将 JSON 字符串转换回 Go 对象
	var unmarshaledUser *User = &User{}
	err = json.Unmarshal(jsonData, unmarshaledUser)
	if err != nil {
		log.Fatalf("Unmarshal 失败: %v", err)
	}

	fmt.Printf("Unmarshal 后的用户对象: %+v\n", unmarshaledUser)
}

// 演示复杂结构的 JSON 操作
func demonstrateComplexJSON() {
	// 创建产品
	products := []Product{
		{ID: 1, Name: "笔记本电脑", Price: 5999.99, Description: "高性能笔记本", InStock: true},
		{ID: 2, Name: "无线鼠标", Price: 99.99, InStock: true},
	}

	// 创建用户
	user := User{
		ID:       1,
		Name:     "李四",
		Email:    "<EMAIL>",
		Age:      30,
		IsActive: true,
		Created:  time.Now(),
	}

	// 创建订单
	order := Order{
		ID:       1001,
		User:     user,
		Products: products,
		Total:    6099.98,
		Status:   "已确认",
		Created:  time.Now(),
	}

	fmt.Printf("原始订单对象: %+v\n", order)

	// Marshal 复杂结构
	orderJSON, err := json.MarshalIndent(order, "", "  ")
	if err != nil {
		log.Fatalf("Marshal 订单失败: %v", err)
	}

	fmt.Printf("订单 JSON:\n%s\n", string(orderJSON))

	// Unmarshal 复杂结构
	var unmarshaledOrder Order
	err = json.Unmarshal(orderJSON, &unmarshaledOrder)
	if err != nil {
		log.Fatalf("Unmarshal 订单失败: %v", err)
	}

	fmt.Printf("Unmarshal 后的订单: %+v\n", unmarshaledOrder)
	fmt.Printf("订单中的用户名: %s\n", unmarshaledOrder.User.Name)
	fmt.Printf("订单中的产品数量: %d\n", len(unmarshaledOrder.Products))
}

// 演示 JSON 标签的使用
func demonstrateJSONTags() {
	// 创建一个用户，但不设置 Tags（演示 omitempty）
	user := User{
		ID:       2,
		Name:     "王五",
		Email:    "<EMAIL>",
		Age:      28,
		IsActive: false,
		Created:  time.Now(),
		// Tags 为空，由于使用了 omitempty，不会出现在 JSON 中
	}

	jsonData, err := json.MarshalIndent(user, "", "  ")
	if err != nil {
		log.Fatalf("Marshal 失败: %v", err)
	}

	fmt.Printf("使用 omitempty 的 JSON（注意 tags 字段不存在）:\n%s\n", string(jsonData))

	// 演示从 JSON 字符串解析
	jsonString := `{
		"id": 3,
		"name": "赵六",
		"email": "<EMAIL>",
		"age": 35,
		"is_active": true,
		"created": "2024-01-15T10:30:00Z",
		"tags": ["管理员", "资深用户"]
	}`

	var parsedUser User
	err = json.Unmarshal([]byte(jsonString), &parsedUser)
	if err != nil {
		log.Fatalf("解析 JSON 字符串失败: %v", err)
	}

	fmt.Printf("从 JSON 字符串解析的用户: %+v\n", parsedUser)
}

// 演示错误处理
func demonstrateErrorHandling() {
	// 1. 无效的 JSON 字符串
	invalidJSON := `{"id": 1, "name": "测试", "age": "不是数字"}`

	var user User
	err := json.Unmarshal([]byte(invalidJSON), &user)
	if err != nil {
		fmt.Printf("解析无效 JSON 时的错误: %v\n", err)
	}

	// 2. 不匹配的结构
	mismatchedJSON := `{"unknown_field": "value", "id": 1}`

	err = json.Unmarshal([]byte(mismatchedJSON), &user)
	if err != nil {
		fmt.Printf("结构不匹配时的错误: %v\n", err)
	} else {
		fmt.Printf("部分匹配的结果: %+v\n", user)
		fmt.Println("注意: 未知字段被忽略，已知字段正常解析")
	}

	// 3. 演示如何处理可能的错误
	fmt.Println("\n安全的 JSON 处理示例:")
	safeJSONProcessing()
}

// 安全的 JSON 处理函数
func safeJSONProcessing() {
	jsonString := `{
		"id": 4,
		"name": "安全用户",
		"email": "<EMAIL>",
		"age": 25,
		"is_active": true,
		"created": "2024-01-15T10:30:00Z"
	}`

	var user User
	if err := json.Unmarshal([]byte(jsonString), &user); err != nil {
		fmt.Printf("JSON 解析失败: %v\n", err)
		return
	}

	fmt.Printf("成功解析用户: %s (ID: %d)\n", user.Name, user.ID)

	// 安全地序列化回 JSON
	if jsonData, err := json.MarshalIndent(user, "", "  "); err != nil {
		fmt.Printf("JSON 序列化失败: %v\n", err)
	} else {
		fmt.Printf("成功序列化:\n%s\n", string(jsonData))
	}
}
